"""
提示词管理服务
"""

from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from langchain_core.messages import HumanMessage, SystemMessage


@dataclass
class TranslationConfig:
    """翻译配置"""

    style: str = "formal"  # formal, informal, casual
    domain: str = "general"  # general, technical, business
    preserve_formatting: bool = True


class PromptManager:
    """提示词管理器"""

    translate_system_message: str = SystemMessage(
        content="你是一个专业的翻译专家；而且你能根据xx-XX识别出语种。"
    )

    def __init__(self):
        """初始化提示词管理器"""

    def create_prompt_template(self, config: TranslationConfig) -> str:
        """
        根据配置创建提示词模板

        Args:
            config: 翻译配置

        Returns:
            str: 提示词模板
        """
        style = config.style if config.style in self.base_templates else "formal"
        domain = (
            config.domain if config.domain in self.base_templates[style] else "general"
        )

        return self.base_templates[style][domain]

    def format_prompt(
        self,
        template: str,
        source_text: str,
        source_language: str = "自动检测",
        target_language: str = "中文",
    ) -> str:
        """
        格式化提示词

        Args:
            template: 提示词模板
            source_text: 源文本
            source_language: 源语言代码或名称
            target_language: 目标语言代码或名称

        Returns:
            str: 格式化后的提示词
        """
        # 转换语言代码为语言名称
        source_lang_name = self.language_names.get(source_language, source_language)
        target_lang_name = self.language_names.get(target_language, target_language)

        return template.format(
            source_text=source_text,
            source_language=source_lang_name,
            target_language=target_lang_name,
        )

    def get_available_domains(self, style: str = "formal") -> list:
        """获取指定风格下可用的领域"""
        if style in self.base_templates:
            return list(self.base_templates[style].keys())
        return list(self.base_templates["formal"].keys())

    def get_translate_language_codes_message(self, language_codes: list) -> list:
        """
        获取翻译语言代码的提示词

        Args:
            language_codes: 语言代码列表

        Returns:
            str: 格式化的提示词
        """
        messages = [
            self.translate_system_message,
            HumanMessage(
                content=f"""请将以下语言代码转换为对应的中文语言名称，并以JSON格式返回。

语言代码列表：{language_codes}

要求：
1. 返回标准的JSON格式
2. 键为原始语言代码，值为对应的中文语言名称
3. 如果遇到不认识的语言代码，请返回"未知语言"
4. 只返回JSON，不要包含任何其他文字

示例格式：
{{"en-US": "英语", "zh-CN": "中文", "ja-JP": "日语"}}

请直接返回JSON结果
"""
            ),
        ]
        return messages

    def validate_config(self, config: TranslationConfig) -> bool:
        """
        验证翻译配置是否有效

        Args:
            config: 翻译配置

        Returns:
            bool: 配置是否有效
        """
        if config.style not in self.base_templates:
            return False

        if config.domain not in self.base_templates[config.style]:
            return False

        return True

    def create_formatted_prompt(
        self,
        source_text: str,
        target_language: str,
        config: TranslationConfig,
        source_language: str = "自动检测",
    ) -> str:
        """
        创建完整的格式化提示词

        Args:
            source_text: 源文本
            target_language: 目标语言
            config: 翻译配置
            source_language: 源语言

        Returns:
            str: 完整的格式化提示词
        """
        template = self.create_prompt_template(config)
        return self.format_prompt(
            template, source_text, source_language, target_language
        )
