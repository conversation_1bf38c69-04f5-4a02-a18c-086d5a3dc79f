"""
翻译API接口层 - 使用DTO和专门的请求处理器实现更清晰的分离
"""

from flask import Blueprint, request, jsonify
import logging

from app.models.common import ApiResponse
from app.services.translate_service import TranslateService
from app.services.excel_service import ExcelService
from app.api.request_handlers import RequestHandlers, RequestValidationError

# 设置日志
logger = logging.getLogger(__name__)

# 创建蓝图
translate_bp = Blueprint("translate", __name__)

# 初始化服务
translate_service = TranslateService()


def handle_api_response(response: ApiResponse, success_code: int = 200):
    """统一处理API响应"""
    if response.is_success():
        return jsonify(response.to_dict()), success_code
    elif response.code == -1 and "任务不存在" in response.message:
        return jsonify(response.to_dict()), 404
    else:
        return jsonify(response.to_dict()), 400


@translate_bp.route("/translate/upload", methods=["POST"])
def upload_excel():
    """Excel文件上传解析接口"""
    try:
        # 提取和验证请求数据
        file_request = RequestHandlers.handle_file_upload(request)

        excel_service = ExcelService()

        # 调用服务层处理业务逻辑
        response = excel_service.parse_and_process_excel(
            filename=file_request.filename,
            file_content=file_request.file_content,
            original_filename=file_request.original_filename,
        )

        return handle_api_response(response)

    except RequestValidationError as e:
        logger.warning(f"文件上传请求验证失败: {str(e)}")
        return jsonify(ApiResponse.error(message=str(e)).to_dict()), 400
    except Exception as e:
        logger.error(f"文件上传处理失败: {str(e)}")
        return (
            jsonify(ApiResponse.error(message=f"服务器内部错误: {str(e)}").to_dict()),
            500,
        )


@translate_bp.route("/translate", methods=["POST"])
def batch_translate():
    """批量翻译接口"""
    try:
        # 提取和验证请求数据
        translate_request = RequestHandlers.handle_batch_translate(request)

        # 调用服务层处理业务逻辑
        response = translate_service.start_batch_translation(translate_request)

        return handle_api_response(response)

    except RequestValidationError as e:
        logger.warning(f"批量翻译请求验证失败: {str(e)}")
        return jsonify(ApiResponse.error(message=str(e)).to_dict()), 400
    except Exception as e:
        logger.error(f"批量翻译处理失败: {str(e)}")
        return (
            jsonify(ApiResponse.error(message=f"服务器内部错误: {str(e)}").to_dict()),
            500,
        )


@translate_bp.route("/history", methods=["GET"])
def get_translation_history():
    """获取翻译历史接口"""
    try:
        # 提取和验证请求参数
        pagination_request = RequestHandlers.handle_pagination(request)

        # 调用服务层处理业务逻辑
        response = translate_service.get_translation_history(
            limit=pagination_request.limit, offset=pagination_request.offset
        )

        return handle_api_response(response)

    except RequestValidationError as e:
        logger.warning(f"获取翻译历史请求验证失败: {str(e)}")
        return jsonify(ApiResponse.error(message=str(e)).to_dict()), 400
    except Exception as e:
        logger.error(f"获取翻译历史处理失败: {str(e)}")
        return (
            jsonify(ApiResponse.error(message=f"服务器内部错误: {str(e)}").to_dict()),
            500,
        )


@translate_bp.route("/history/<task_id>/results", methods=["GET"])
def get_translation_results(task_id):
    """获取翻译结果接口"""
    try:
        # 验证路径参数
        task_request = RequestHandlers.handle_task_id(task_id)

        # 调用服务层处理业务逻辑
        response = translate_service.get_translation_results(task_request.task_id)

        return handle_api_response(response)

    except RequestValidationError as e:
        logger.warning(f"获取翻译结果请求验证失败: {str(e)}")
        return jsonify(ApiResponse.error(message=str(e)).to_dict()), 400
    except Exception as e:
        logger.error(f"获取翻译结果处理失败: {str(e)}")
        return (
            jsonify(ApiResponse.error(message=f"服务器内部错误: {str(e)}").to_dict()),
            500,
        )
