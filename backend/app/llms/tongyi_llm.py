"""
通义千问大模型实现
"""

import os
import logging
from typing import Optional

logger = logging.getLogger(__name__)

try:
    from langchain_community.chat_models import ChatTongyi

    LANGCHAIN_AVAILABLE = True
except ImportError:
    logger.warning("langchain相关包未安装，通义千问模型将不可用")
    LANGCHAIN_AVAILABLE = False


QWEN_TEMPERATURE = 0.7
QWEN_MAX_TOKENS = 2000
QWEN_MODEL_NAME = "qwen-max"


def create_tongyi_llm(
    api_key: Optional[str] = os.getenv("QWEN_API_KEY"),
    base_url: Optional[str] = os.getenv("QWEN_BASE_URL"),
    model_name: Optional[str] = None,
    temperature: Optional[float] = None,
    max_tokens: Optional[int] = None,
) -> ChatTongyi:
    """
    创建通义千问大模型实例

    Args:
        api_key: API密钥，默认从环境变量QWEN_API_KEY获取
        model_name: 模型名称，默认从环境变量QWEN_MODEL_NAME获取，或使用qwen-turbo
        temperature: 温度参数，默认从环境变量QWEN_TEMPERATURE获取，或使用0.1
        max_tokens: 最大token数，默认从环境变量QWEN_MAX_TOKENS获取，或使用2000

    Returns:
        ChatTongyi: 通义千问模型实例

    Raises:
        RuntimeError: langchain包未安装
        ValueError: API密钥未配置
    """

    if not LANGCHAIN_AVAILABLE:
        raise RuntimeError("langchain相关包未安装，无法使用通义千问模型")

    if not api_key:
        raise ValueError(
            "通义千问API密钥未配置，请在数据库configuration表中设置QWEN_API_KEY或设置环境变量"
        )

    try:
        llm = ChatTongyi(
            base_url=base_url,
            dashscope_api_key=api_key,
            model_name=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
        )
        logger.info(f"通义千问模型初始化成功: {model_name}")
        return llm
    except Exception as e:
        logger.error(f"初始化通义千问模型失败: {str(e)}")
        raise
