#!/usr/bin/env python3
"""
Code枚举测试脚本
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.models.common import Code


def test_code_enum():
    """测试Code枚举功能"""
    print("🧪 测试Code枚举功能...")
    
    # 测试枚举值
    print(f"✅ Code.SUCCESS = {Code.SUCCESS} (类型: {type(Code.SUCCESS)})")
    print(f"✅ Code.ERROR = {Code.ERROR} (类型: {type(Code.ERROR)})")
    
    # 测试整数比较
    assert Code.SUCCESS == 0, "SUCCESS应该等于0"
    assert Code.ERROR == 1, "ERROR应该等于1"
    print("✅ 整数值比较正常")
    
    # 测试字符串表示
    print(f"✅ str(Code.SUCCESS) = '{str(Code.SUCCESS)}'")
    print(f"✅ str(Code.ERROR) = '{str(Code.ERROR)}'")
    
    # 测试消息属性
    print(f"✅ Code.SUCCESS.message = '{Code.SUCCESS.message}'")
    print(f"✅ Code.ERROR.message = '{Code.ERROR.message}'")
    
    # 测试工具方法
    assert Code.is_success(Code.SUCCESS), "is_success方法应该返回True"
    assert not Code.is_success(Code.ERROR), "is_success方法应该返回False"
    assert Code.is_error(Code.ERROR), "is_error方法应该返回True"
    assert not Code.is_error(Code.SUCCESS), "is_error方法应该返回False"
    print("✅ 工具方法测试正常")
    
    # 测试在条件语句中的使用
    code = Code.SUCCESS
    if code == Code.SUCCESS:
        print("✅ 条件判断测试正常")
    
    # 测试在字典中的使用
    result_map = {
        Code.SUCCESS: "操作成功",
        Code.ERROR: "操作失败"
    }
    print(f"✅ 字典使用测试: {result_map[Code.SUCCESS]}")
    
    # 测试JSON序列化兼容性
    import json
    data = {
        "code": int(Code.SUCCESS),
        "message": Code.SUCCESS.message
    }
    json_str = json.dumps(data)
    print(f"✅ JSON序列化测试: {json_str}")
    
    print("🎉 所有Code枚举测试通过！")


def demo_usage():
    """演示Code枚举的使用方法"""
    print("\n📖 Code枚举使用示例:")
    
    # 示例1: 在API响应中使用
    def create_response(success=True, data=None, message=None):
        code = Code.SUCCESS if success else Code.ERROR
        return {
            "code": int(code),
            "message": message or code.message,
            "data": data
        }
    
    success_response = create_response(True, {"user_id": 123}, "用户创建成功")
    error_response = create_response(False, None, "用户创建失败")
    
    print("成功响应:", success_response)
    print("错误响应:", error_response)
    
    # 示例2: 在业务逻辑中使用
    def process_data(data):
        if not data:
            return Code.ERROR, "数据不能为空"
        
        # 模拟处理逻辑
        if len(data) > 10:
            return Code.ERROR, "数据长度超出限制"
        
        return Code.SUCCESS, "数据处理成功"
    
    # 测试不同情况
    test_cases = [
        "",           # 空数据
        "hello",      # 正常数据
        "a" * 15      # 超长数据
    ]
    
    for i, test_data in enumerate(test_cases):
        code, message = process_data(test_data)
        status = "成功" if Code.is_success(code) else "失败"
        print(f"测试{i+1}: 状态={status}, 代码={code}, 消息={message}")


if __name__ == "__main__":
    test_code_enum()
    demo_usage()
