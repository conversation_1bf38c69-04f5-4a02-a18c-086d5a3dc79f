# Code枚举使用指南

## 概述

新建了一个Code枚举类，用于统一管理应用中的响应代码。枚举值定义如下：
- `Code.SUCCESS = 0` - 表示操作成功
- `Code.ERROR = 1` - 表示操作失败

## 枚举定义

```python
from enum import IntEnum

class Code(IntEnum):
    SUCCESS = 0  # 成功
    ERROR = 1    # 错误
```

## 导入方式

```python
from app.models.common import Code
```

## 基本使用

### 1. 直接使用枚举值

```python
from app.models.common import Code

# 获取枚举值
success_code = Code.SUCCESS  # 0
error_code = Code.ERROR      # 1

# 整数比较
if code == 0:
    print("成功")
elif code == 1:
    print("失败")

# 枚举比较（推荐）
if code == Code.SUCCESS:
    print("成功")
elif code == Code.ERROR:
    print("失败")
```

### 2. 获取消息

```python
# 获取默认消息
success_msg = Code.SUCCESS.message  # "操作成功"
error_msg = Code.ERROR.message      # "操作失败"
```

### 3. 工具方法

```python
# 检查是否成功
Code.is_success(Code.SUCCESS)  # True
Code.is_success(Code.ERROR)    # False

# 检查是否失败
Code.is_error(Code.ERROR)      # True
Code.is_error(Code.SUCCESS)    # False
```

## 在API响应中使用

### 创建统一响应格式

```python
from app.models.common import Code

def create_api_response(success=True, data=None, message=None):
    """创建统一的API响应格式"""
    code = Code.SUCCESS if success else Code.ERROR
    return {
        "code": int(code),
        "message": message or code.message,
        "data": data,
        "success": Code.is_success(code)
    }

# 使用示例
success_response = create_api_response(True, {"user_id": 123}, "用户创建成功")
# 结果: {"code": 0, "message": "用户创建成功", "data": {"user_id": 123}, "success": true}

error_response = create_api_response(False, None, "用户不存在")
# 结果: {"code": 1, "message": "用户不存在", "data": null, "success": false}
```

### 在Flask路由中使用

```python
from flask import jsonify
from app.models.common import Code

@app.route('/api/users', methods=['POST'])
def create_user():
    try:
        # 业务逻辑
        user = create_user_logic()
        
        return jsonify({
            "code": int(Code.SUCCESS),
            "message": "用户创建成功",
            "data": {"user_id": user.id}
        }), 200
        
    except Exception as e:
        return jsonify({
            "code": int(Code.ERROR),
            "message": f"用户创建失败: {str(e)}",
            "data": None
        }), 500
```

## 在业务逻辑中使用

### 返回操作结果

```python
from app.models.common import Code

def validate_user_data(data):
    """验证用户数据"""
    if not data:
        return Code.ERROR, "数据不能为空"
    
    if not data.get('email'):
        return Code.ERROR, "邮箱不能为空"
    
    if len(data.get('password', '')) < 6:
        return Code.ERROR, "密码长度不能少于6位"
    
    return Code.SUCCESS, "数据验证通过"

# 使用示例
user_data = {"email": "<EMAIL>", "password": "123456"}
code, message = validate_user_data(user_data)

if Code.is_success(code):
    print(f"验证成功: {message}")
else:
    print(f"验证失败: {message}")
```

### 在服务层中使用

```python
from app.models.common import Code

class UserService:
    def create_user(self, user_data):
        """创建用户"""
        # 验证数据
        code, message = self.validate_user_data(user_data)
        if Code.is_error(code):
            return code, message, None
        
        try:
            # 创建用户逻辑
            user = self.save_user(user_data)
            return Code.SUCCESS, "用户创建成功", user
        except Exception as e:
            return Code.ERROR, f"用户创建失败: {str(e)}", None
    
    def get_user(self, user_id):
        """获取用户"""
        user = self.find_user_by_id(user_id)
        if user:
            return Code.SUCCESS, "用户查询成功", user
        else:
            return Code.ERROR, "用户不存在", None
```

## 在数据库操作中使用

```python
from app.models.common import Code
from app.database import execute, fetch_one

def create_user_record(name, email):
    """创建用户记录"""
    try:
        # 检查邮箱是否已存在
        existing = fetch_one("SELECT id FROM users WHERE email = %s", (email,))
        if existing:
            return Code.ERROR, "邮箱已存在", None
        
        # 插入新用户
        affected_rows = execute(
            "INSERT INTO users (name, email) VALUES (%s, %s)",
            (name, email)
        )
        
        if affected_rows > 0:
            return Code.SUCCESS, "用户创建成功", affected_rows
        else:
            return Code.ERROR, "用户创建失败", None
            
    except Exception as e:
        return Code.ERROR, f"数据库操作失败: {str(e)}", None
```

## JSON序列化

Code枚举可以直接用于JSON序列化：

```python
import json
from app.models.common import Code

# 创建包含Code的数据
response_data = {
    "code": int(Code.SUCCESS),  # 转换为整数
    "message": Code.SUCCESS.message,
    "timestamp": "2025-07-31T18:30:00Z"
}

# JSON序列化
json_string = json.dumps(response_data)
print(json_string)
# 输出: {"code": 0, "message": "操作成功", "timestamp": "2025-07-31T18:30:00Z"}
```

## 最佳实践

1. **统一使用枚举**：在整个项目中统一使用Code枚举，避免硬编码数字
2. **类型转换**：在JSON响应中使用`int(Code.SUCCESS)`确保序列化正确
3. **工具方法**：使用`Code.is_success()`和`Code.is_error()`进行状态判断
4. **错误处理**：在异常处理中统一返回Code.ERROR
5. **文档说明**：在API文档中明确说明code字段的含义

## 扩展性

如果将来需要添加更多状态码，可以轻松扩展：

```python
class Code(IntEnum):
    SUCCESS = 0      # 成功
    ERROR = 1        # 错误
    WARNING = 2      # 警告
    UNAUTHORIZED = 3 # 未授权
    FORBIDDEN = 4    # 禁止访问
    NOT_FOUND = 5    # 未找到
```

## 测试

运行测试脚本验证功能：

```bash
cd backend
python test_code_enum.py
```

预期输出：
```
🎉 所有Code枚举测试通过！
```
