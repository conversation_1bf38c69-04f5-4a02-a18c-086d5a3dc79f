<template>
  <div
    class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 overflow-y-auto"
  >
    <!-- 导航栏 -->
    <nav
      class="bg-white/80 backdrop-blur-md shadow-sm border-b border-white/20 sticky top-0 z-50"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center space-x-3">
            <div
              class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg"
            >
              <font-awesome-icon icon="language" class="text-white text-lg" />
            </div>
            <h1
              class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent"
            >
              Excel 翻译工具
            </h1>
          </div>

          <div class="flex items-center space-x-1">
            <router-link
              to="/"
              class="flex items-center space-x-2 px-4 py-2 text-blue-600 bg-blue-50 rounded-lg font-medium"
            >
              <font-awesome-icon icon="home" class="text-sm" />
              <span class="font-medium">首页</span>
            </router-link>
            <router-link
              to="/legacy"
              class="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
            >
              <font-awesome-icon icon="eye" class="text-sm" />
              <span>原版界面</span>
            </router-link>
            <router-link
              to="/history"
              class="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
            >
              <font-awesome-icon icon="history" class="text-sm" />
              <span class="font-medium">历史记录</span>
            </router-link>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-16">
      <!-- 步骤指示器 -->
      <div class="mb-8">
        <div class="flex items-center justify-center space-x-4 mb-6">
          <div
            v-for="(step, index) in steps"
            :key="step.id"
            class="flex items-center"
          >
            <div
              :class="[
                'flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300',
                currentStep >= index + 1
                  ? 'bg-blue-500 border-blue-500 text-white'
                  : 'bg-white border-gray-300 text-gray-400',
              ]"
            >
              <font-awesome-icon
                v-if="currentStep > index + 1"
                icon="check"
                class="text-sm"
              />
              <font-awesome-icon v-else :icon="step.icon" class="text-sm" />
            </div>
            <div class="ml-3 hidden sm:block">
              <p
                :class="[
                  'text-sm font-medium',
                  currentStep >= index + 1 ? 'text-blue-600' : 'text-gray-500',
                ]"
              >
                {{ step.title }}
              </p>
            </div>
            <div
              v-if="index < steps.length - 1"
              :class="[
                'w-12 h-0.5 mx-4 transition-all duration-300',
                currentStep > index + 1 ? 'bg-blue-500' : 'bg-gray-300',
              ]"
            ></div>
          </div>
        </div>
      </div>

      <!-- 步骤内容 -->
      <div class="space-y-8">
        <!-- 步骤1: 文件上传 -->
        <div
          v-show="currentStep === StepEnum.FILE_UPLOAD"
          class="animate-fade-in"
        >
          <FileUploadStep
            @file-uploaded="handleFileUploaded"
            :loading="loading"
          />
        </div>

        <!-- 步骤2: 翻译管理（融合预览和配置） -->
        <div
          v-show="currentStep === StepEnum.TRANSLATION_MANAGEMENT"
          class="animate-fade-in"
        >
          <TranslationManagementStep
            :excel-data="excelData"
            :language-names="excelData?.language_names"
            @start-translation="handleStartTranslation"
            @back="handleRestart"
            @restart="handleRestart"
          />
        </div>

        <!-- 步骤4: 翻译进度 -->
        <div v-show="currentStep === StepEnum.PROGRESS" class="animate-fade-in">
          <ProgressStep
            :progress="translationProgress"
            :status="translationStatus"
            @complete="handleTranslationComplete"
          />
        </div>

        <!-- 步骤5: 结果下载 -->
        <div v-show="currentStep === StepEnum.RESULT" class="animate-fade-in">
          <ResultStep
            :result-data="translationResult"
            @restart="handleRestart"
          />
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { ExcelData } from '@/types';
import { translateApiService } from '@/api/translate';
import FileUploadStep from '@/components/steps/FileUploadStep.vue';
import TranslationManagementStep from '@/components/steps/TranslationManagementStep.vue';
import ProgressStep from '@/components/steps/ProgressStep.vue';
import ResultStep from '@/components/steps/ResultStep.vue';

enum StepEnum {
  FILE_UPLOAD = 1,
  TRANSLATION_MANAGEMENT = 2,
  PROGRESS = 3,
  RESULT = 4,
}

interface StepInfo {
  id: StepEnum;
  title: string;
  icon: string;
}

// 步骤定义
const steps: StepInfo[] = [
  { id: StepEnum.FILE_UPLOAD, title: '上传文件', icon: 'upload' },
  { id: StepEnum.TRANSLATION_MANAGEMENT, title: '翻译管理', icon: 'cog' },
  { id: StepEnum.PROGRESS, title: '翻译进度', icon: 'spinner' },
  { id: StepEnum.RESULT, title: '下载结果', icon: 'download' },
];

// 响应式数据
const currentStep = ref(StepEnum.FILE_UPLOAD);
const loading = ref(false);
const excelData = ref<ExcelData | null>(null);
const translationProgress = ref(0);
const translationStatus = ref<'idle' | 'processing' | 'completed' | 'error'>(
  'idle'
);
const translationResult = ref<any>(null);

// 处理文件上传成功 - 跳转到翻译管理步骤
const handleFileUploaded = (data: ExcelData) => {
  excelData.value = data;
  currentStep.value = StepEnum.TRANSLATION_MANAGEMENT;
};

// 注释: 准备批处理数据 - 将Excel数据转换为新的JSON格式
const prepareBatchesData = (config: any) => {
  if (!excelData.value) {
    return [];
  }

  const sourceLanguage = config.sourceLanguage;
  const translations = excelData.value.translations;
  const batchSize = 10; // 每批处理的条目数量
  const batches = [];
  const items = [];

  // 注释: 遍历Excel数据，为每个条目构建翻译项
  for (const [key, translationRow] of Object.entries(translations)) {
    const sourceText = translationRow[sourceLanguage];

    // 注释: 跳过空白条目（如果配置了skipEmpty）
    if (config.skipEmpty && (!sourceText || sourceText.trim() === '')) {
      continue;
    }

    // 注释: 构建现有翻译映射
    const existingTranslations: Record<string, string | null> = {};
    for (const lang of excelData.value.languages) {
      const translation = translationRow[lang];
      existingTranslations[lang] = translation || null;
    }

    // 注释: 过滤需要翻译的目标语言
    const targetLanguages = config.targetLanguages.filter(
      (targetLang: string) => {
        const existingText = translationRow[targetLang];
        // 如果不覆盖已有翻译且已有内容，则跳过
        return (
          config.overwriteExisting ||
          !existingText ||
          existingText.trim() === ''
        );
      }
    );

    // 注释: 如果有需要翻译的目标语言，则添加到items中
    if (targetLanguages.length > 0) {
      items.push({
        key: key,
        sourceText: sourceText || '',
        sourceLanguage: sourceLanguage,
        targetLanguages: targetLanguages,
        context: `翻译键: ${key}`, // 提供上下文信息
        existingTranslations: existingTranslations,
      });
    }
  }

  // 注释: 将items分批处理
  for (let i = 0; i < items.length; i += batchSize) {
    const batchItems = items.slice(i, i + batchSize);
    batches.push({
      batchId: `batch_${String(Math.floor(i / batchSize) + 1).padStart(3, '0')}`,
      items: batchItems,
    });
  }

  return batches;
};

// 模拟翻译进度
const simulateTranslationProgress = () => {
  const interval = setInterval(() => {
    translationProgress.value += Math.random() * 15;
    if (translationProgress.value >= 100) {
      translationProgress.value = 100;
      translationStatus.value = 'completed';
      clearInterval(interval);
      setTimeout(() => {
        handleTranslationComplete();
      }, 1000);
    }
  }, 500);
};

// 处理开始翻译 - 发送翻译请求到后端
const handleStartTranslation = async (configData: any) => {
  currentStep.value = StepEnum.PROGRESS;
  translationStatus.value = 'processing';
  translationProgress.value = 0;

  console.log('翻译配置数据:', configData);

  try {
    // 注释: 从传入的配置数据中提取原始配置和翻译请求模板
    const { originalConfig, translationRequest } = configData;

    // 注释: 构建完整的翻译请求数据，包含batches信息
    const fullTranslationRequest = {
      ...translationRequest,
      batches: prepareBatchesData(originalConfig),
    };

    console.log('发送翻译请求:', fullTranslationRequest);

    // 注释: 发送新格式的翻译请求到Flask后端
    const response = await translateApiService.startBatchTranslation(
      fullTranslationRequest
    );

    console.log('翻译请求响应:', response);

    // 模拟翻译进度（实际项目中应该通过WebSocket或轮询获取进度）
    simulateTranslationProgress();
  } catch (error) {
    console.error('翻译请求失败:', error);
    translationStatus.value = 'error';
  }
};

// 处理翻译完成 - 生成假的结果数据
const handleTranslationComplete = () => {
  translationResult.value = {
    downloadUrl: '#',
    filename: `translated_${excelData.value?.metadata.filename || 'file.xlsx'}`,
    statistics: {
      totalKeys: excelData.value?.metadata.total_keys || 150,
      translatedKeys: excelData.value?.metadata.total_keys || 150,
      languages: excelData.value?.languages.length || 5,
    },
  };
  currentStep.value = StepEnum.RESULT;
};

// 重新开始
const handleRestart = () => {
  currentStep.value = 1;
  excelData.value = null;
  translationProgress.value = 0;
  translationStatus.value = 'idle';
  translationResult.value = null;
};
</script>

<style scoped>
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
